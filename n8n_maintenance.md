# n8n 工作流自动化维护文档

**应用名称**: n8n
**版本**: 1.104.2 (latest)
**服务器**: 110.40.44.80
**创建时间**: 2025年8月1日
**最后升级**: 2025年8月1日 09:42

---

## 📋 基本信息

### 应用概述
- **功能**: 工作流自动化平台，支持API集成和数据处理
- **容器名**: n8n
- **镜像**: n8nio/n8n:latest
- **运行状态**: 正常运行 (已升级到v1.104.2)
- **官网**: https://n8n.io/
- **GitHub**: https://github.com/n8n-io/n8n

### 访问信息
- **主要访问地址**: http://110.40.44.80:5678
- **宝塔反向代理**: n8n:23cc.cn
- **容器内部端口**: 5678

---

## 🔌 端口配置

| 宿主机端口 | 容器端口 | 用途 | 协议 |
|-----------|---------|------|------|
| 5678 | 5678 | Web界面和API | HTTP |

---

## 🛠️ 维护命令

### 基本操作
```bash
# 查看容器状态
docker ps | grep n8n

# 查看容器详细信息
docker inspect n8n

# 查看实时日志
docker logs -f n8n

# 查看最近100行日志
docker logs --tail 100 n8n

# 重启容器
docker restart n8n

# 停止容器
docker stop n8n

# 启动容器
docker start n8n
```

### 进入容器调试
```bash
# 进入容器shell
docker exec -it n8n sh

# 查看容器内进程
docker exec n8n ps aux

# 查看n8n进程
docker exec n8n ps aux | grep n8n

# 检查Node.js版本
docker exec n8n node --version

# 检查npm包
docker exec n8n npm list --depth=0
```

### 健康检查
```bash
# 测试Web服务
curl -I http://110.40.44.80:5678

# 测试API端点
curl -X GET http://110.40.44.80:5678/rest/active-workflows

# 检查响应时间
curl -w "@curl-format.txt" -o /dev/null -s http://110.40.44.80:5678

# 测试登录页面
curl -s http://110.40.44.80:5678 | grep -i "n8n"
```

---

## 📊 监控指标

### 资源使用
```bash
# 查看容器资源使用
docker stats n8n

# 查看容器磁盘使用
docker exec n8n df -h

# 查看容器内存使用
docker exec n8n free -h 2>/dev/null || echo "free命令不可用"

# 查看进程内存使用
docker exec n8n ps aux --sort=-%mem | head -10
```

### 性能监控
- **CPU使用率**: 通常在1-5%，执行工作流时可能更高
- **内存使用**: 约200MB-1GB，取决于工作流复杂度
- **启动时间**: 约10-20秒
- **并发处理**: 支持多个工作流同时执行

---

## 🔧 工作流管理

### 工作流操作
```bash
# 导出所有工作流
docker exec n8n n8n export:workflow --all --output=/tmp/workflows.json

# 导出特定工作流
docker exec n8n n8n export:workflow --id=1 --output=/tmp/workflow-1.json

# 导入工作流
docker exec n8n n8n import:workflow --input=/tmp/workflows.json

# 列出所有工作流
docker exec n8n n8n list:workflow

# 激活工作流
docker exec n8n n8n update:workflow --id=1 --active=true

# 停用工作流
docker exec n8n n8n update:workflow --id=1 --active=false
```

### 凭据管理
```bash
# 导出凭据 (加密)
docker exec n8n n8n export:credentials --all --output=/tmp/credentials.json

# 导入凭据
docker exec n8n n8n import:credentials --input=/tmp/credentials.json

# 列出凭据
docker exec n8n n8n list:credentials
```

---

## 🔧 故障排查

### 常见问题

#### 1. 容器启动失败
```bash
# 检查容器日志
docker logs n8n

# 检查端口占用
netstat -tlnp | grep 5678

# 检查镜像是否正常
docker images | grep n8n

# 检查容器配置
docker inspect n8n | grep -A 10 "Config"
```

#### 2. 工作流执行失败
```bash
# 查看执行日志
docker logs n8n | grep -i "error\|failed"

# 检查工作流状态
docker exec n8n n8n list:workflow

# 查看特定工作流日志
docker exec n8n n8n execute:workflow --id=1

# 测试工作流连接
docker exec n8n n8n test:workflow --id=1
```

#### 3. API连接问题
```bash
# 测试API响应
curl -X GET http://110.40.44.80:5678/rest/active-workflows

# 检查API认证
curl -X GET -H "Authorization: Bearer YOUR_TOKEN" http://110.40.44.80:5678/rest/workflows

# 测试Webhook
curl -X POST http://110.40.44.80:5678/webhook/test
```

#### 4. 内存或性能问题
```bash
# 检查内存使用
docker stats n8n --no-stream

# 查看运行中的工作流
docker exec n8n n8n list:workflow --active

# 重启容器释放内存
docker restart n8n
```

### 日志分析
```bash
# 查找错误日志
docker logs n8n 2>&1 | grep -i error

# 查找工作流执行日志
docker logs n8n 2>&1 | grep -i "workflow\|execution"

# 查找API调用日志
docker logs n8n 2>&1 | grep -i "api\|rest"

# 查找特定时间段日志
docker logs n8n --since="2025-08-01T09:00:00" --until="2025-08-01T10:00:00"
```

---

## 💾 备份与恢复

### 数据备份
```bash
# 创建备份目录
mkdir -p ./backup/n8n-$(date +%Y%m%d)

# 备份工作流
docker exec n8n n8n export:workflow --all --output=/tmp/workflows-backup.json
docker cp n8n:/tmp/workflows-backup.json ./backup/n8n-$(date +%Y%m%d)/

# 备份凭据
docker exec n8n n8n export:credentials --all --output=/tmp/credentials-backup.json
docker cp n8n:/tmp/credentials-backup.json ./backup/n8n-$(date +%Y%m%d)/

# 备份配置文件 (如果有持久化存储)
docker cp n8n:/home/<USER>/.n8n ./backup/n8n-$(date +%Y%m%d)/n8n-config

# 备份数据库 (如果使用外部数据库)
# docker exec n8n-db mysqldump -u root -p n8n > ./backup/n8n-$(date +%Y%m%d)/n8n-db.sql
```

### 配置备份
```bash
# 导出容器配置
docker inspect n8n > ./backup/n8n-$(date +%Y%m%d)/n8n-container-config.json

# 备份环境变量
docker exec n8n env > ./backup/n8n-$(date +%Y%m%d)/n8n-env.txt

# 备份启动命令
docker inspect n8n | jq '.[0].Config.Cmd' > ./backup/n8n-$(date +%Y%m%d)/n8n-cmd.json
```

### 恢复操作
```bash
# 恢复工作流
docker cp ./backup/n8n-YYYYMMDD/workflows-backup.json n8n:/tmp/
docker exec n8n n8n import:workflow --input=/tmp/workflows-backup.json

# 恢复凭据
docker cp ./backup/n8n-YYYYMMDD/credentials-backup.json n8n:/tmp/
docker exec n8n n8n import:credentials --input=/tmp/credentials-backup.json

# 重启容器应用更改
docker restart n8n
```

---

## 🔄 更新维护

### 版本更新
```bash
# 1. 备份当前版本
docker commit n8n n8n-backup-$(date +%Y%m%d)

# 2. 备份工作流和凭据
mkdir -p /backup/n8n-upgrade-$(date +%Y%m%d)
docker exec n8n n8n export:workflow --all --output=/tmp/workflows-backup.json
docker cp n8n:/tmp/workflows-backup.json /backup/n8n-upgrade-$(date +%Y%m%d)/
docker exec n8n n8n export:credentials --all --output=/tmp/credentials-backup.json
docker cp n8n:/tmp/credentials-backup.json /backup/n8n-upgrade-$(date +%Y%m%d)/

# 3. 拉取最新镜像
docker pull n8nio/n8n:latest

# 4. 停止并删除旧容器
docker stop n8n && docker rm n8n

# 5. 使用新镜像启动容器（推荐配置）
docker run -d --name n8n \
  -p 5678:5678 \
  -e N8N_RUNNERS_ENABLED=true \
  -e N8N_ENFORCE_SETTINGS_FILE_PERMISSIONS=true \
  --restart unless-stopped \
  n8nio/n8n:latest

# 注意：启用任务运行器后，日志会显示：
# - n8n Task Broker ready on 127.0.0.1, port 5679
# - Registered runner "JS Task Runner"

# 6. 恢复数据
docker cp /backup/n8n-upgrade-$(date +%Y%m%d)/workflows-backup.json n8n:/tmp/
docker exec n8n n8n import:workflow --input=/tmp/workflows-backup.json
docker cp /backup/n8n-upgrade-$(date +%Y%m%d)/credentials-backup.json n8n:/tmp/
docker exec n8n n8n import:credentials --input=/tmp/credentials-backup.json

# 7. 重启容器确保数据加载
docker restart n8n
```

### 定期维护脚本
```bash
#!/bin/bash
# n8n 定期维护脚本

echo "$(date): 开始 n8n 定期维护"

# 检查容器状态
STATUS=$(docker inspect n8n | jq -r '.[0].State.Status')
echo "容器状态: $STATUS"

if [ "$STATUS" != "running" ]; then
    echo "容器未运行，尝试启动"
    docker start n8n
    sleep 10
fi

# 检查服务响应
if curl -f -s http://110.40.44.80:5678 > /dev/null; then
    echo "n8n 服务响应正常"
else
    echo "n8n 服务无响应，重启容器"
    docker restart n8n
    sleep 20
fi

# 备份工作流 (每周执行)
if [ $(date +%u) -eq 1 ]; then
    echo "执行每周备份"
    mkdir -p ./backup/n8n-weekly-$(date +%Y%m%d)
    docker exec n8n n8n export:workflow --all --output=/tmp/workflows-weekly.json
    docker cp n8n:/tmp/workflows-weekly.json ./backup/n8n-weekly-$(date +%Y%m%d)/
fi

# 清理旧备份 (保留最近4周)
find ./backup -name "n8n-weekly-*" -type d -mtime +28 -exec rm -rf {} \;

echo "$(date): n8n 维护完成"
```

---

## 📈 性能优化

### 资源限制
```bash
# 设置内存限制
docker update --memory=1g n8n

# 设置CPU限制
docker update --cpus=1 n8n
```

### 工作流优化
- **避免长时间运行的工作流**: 设置合理的超时时间
- **优化数据处理**: 分批处理大量数据
- **使用缓存**: 合理使用Set和Get节点缓存数据
- **监控执行时间**: 定期检查工作流执行效率

### 监控脚本
```bash
#!/bin/bash
# n8n 性能监控脚本

echo "=== n8n 性能监控 ==="
echo "时间: $(date)"

# 容器状态
echo "容器状态: $(docker ps --format 'table {{.Status}}' | grep n8n)"

# 资源使用
echo "内存使用: $(docker stats --no-stream --format 'table {{.MemUsage}}' n8n)"
echo "CPU使用: $(docker stats --no-stream --format 'table {{.CPUPerc}}' n8n)"

# 活跃工作流数量
ACTIVE_WORKFLOWS=$(docker exec n8n n8n list:workflow --active 2>/dev/null | wc -l)
echo "活跃工作流: $ACTIVE_WORKFLOWS 个"

# 服务响应时间
RESPONSE_TIME=$(curl -w "%{time_total}" -o /dev/null -s http://110.40.44.80:5678)
echo "响应时间: ${RESPONSE_TIME}s"
```

---

## 🚨 应急处理

### 快速重启
```bash
# 快速重启并验证
docker restart n8n && sleep 20 && curl -I http://110.40.44.80:5678
```

### 应急回滚
```bash
# 回滚到备份版本
docker stop n8n
docker rm n8n
docker run -d --name n8n [原始参数] n8n-backup-YYYYMMDD
```

### 数据恢复
```bash
# 紧急恢复工作流
docker cp ./backup/n8n-latest/workflows-backup.json n8n:/tmp/
docker exec n8n n8n import:workflow --input=/tmp/workflows-backup.json
docker restart n8n
```

---

## 📞 维护计划

### 日常检查 (每日)
- [ ] 检查容器运行状态
- [ ] 验证Web界面访问
- [ ] 查看错误日志
- [ ] 检查活跃工作流状态

### 周期维护 (每周)
- [ ] 备份工作流和凭据
- [ ] 检查资源使用情况
- [ ] 清理旧日志文件
- [ ] 测试关键工作流

### 月度维护 (每月)
- [ ] 检查版本更新
- [ ] 性能评估和优化
- [ ] 备份验证和测试
- [ ] 文档更新

---

## 🔗 相关链接

- **官方文档**: https://docs.n8n.io/
- **社区论坛**: https://community.n8n.io/
- **Docker Hub**: https://hub.docker.com/r/n8nio/n8n
- **GitHub Issues**: https://github.com/n8n-io/n8n/issues

---

## 更新日志
- 2025-08-01: 创建 n8n 维护文档
- 2025-08-01: 添加工作流管理和备份恢复指南
- 2025-08-01: 完善故障排查和性能优化方案
- 2025-08-01 09:42: 成功升级到 v1.104.2
  - 从 v1.104.1 升级到 v1.104.2
  - 备份并恢复了3个工作流和5个凭据
  - 启用了任务运行器 (JS Task Runner) 和权限强制检查
  - Task Broker运行在端口5679
  - 更新了升级流程文档和最佳实践配置
