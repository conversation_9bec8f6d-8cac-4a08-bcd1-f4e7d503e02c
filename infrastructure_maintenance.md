# 基础设施维护文档

**服务器**: ************  
**创建时间**: 2025年8月1日

---

## 📋 基础设施概览

### 核心基础服务

#### 1. Elasticsearch 搜索引擎
- **容器名**: ragflow-es-01
- **镜像**: elasticsearch:8.11.3
- **端口**: 1200:9200
- **用途**: RAG Flow文档索引和全文搜索
- **宝塔代理**: ragflow-mcp:23cc.cn

#### 2. MinIO 对象存储
- **容器名**: ragflow-minio
- **镜像**: minio/minio
- **端口**: 9000-9001:9000-9001
- **用途**: 文档文件存储和媒体资源管理
- **管理界面**: http://************:9001

---

## 🔍 Elasticsearch 维护

### 基本操作
```bash
# 查看Elasticsearch状态
docker ps | grep ragflow-es

# 查看Elasticsearch日志
docker logs ragflow-es-01

# 重启Elasticsearch
docker restart ragflow-es-01

# 进入容器
docker exec -it ragflow-es-01 bash
```

### 健康检查
```bash
# 检查集群健康状态
curl -X GET "************:1200/_cluster/health?pretty"

# 查看节点信息
curl -X GET "************:1200/_nodes?pretty"

# 查看索引列表
curl -X GET "************:1200/_cat/indices?v"

# 查看集群统计
curl -X GET "************:1200/_cluster/stats?pretty"
```

### 索引管理
```bash
# 查看所有索引
curl -X GET "************:1200/_cat/indices?v&s=store.size:desc"

# 查看索引设置
curl -X GET "************:1200/your_index/_settings?pretty"

# 查看索引映射
curl -X GET "************:1200/your_index/_mapping?pretty"

# 删除索引 (谨慎操作)
# curl -X DELETE "************:1200/old_index"

# 重建索引
curl -X POST "************:1200/your_index/_reindex" -H 'Content-Type: application/json' -d'
{
  "source": {
    "index": "old_index"
  },
  "dest": {
    "index": "new_index"
  }
}'
```

### 性能监控
```bash
# 查看性能统计
curl -X GET "************:1200/_stats?pretty"

# 查看搜索性能
curl -X GET "************:1200/_stats/search?pretty"

# 查看索引性能
curl -X GET "************:1200/_stats/indexing?pretty"

# 查看内存使用
curl -X GET "************:1200/_nodes/stats/jvm?pretty"
```

### 故障排查
```bash
# 查看错误日志
docker logs ragflow-es-01 | grep -i error

# 检查磁盘空间
curl -X GET "************:1200/_cat/allocation?v"

# 查看慢查询
curl -X GET "************:1200/_cat/pending_tasks?v"

# 清理缓存
curl -X POST "************:1200/_cache/clear"
```

---

## 📦 MinIO 对象存储维护

### 基本操作
```bash
# 查看MinIO状态
docker ps | grep ragflow-minio

# 查看MinIO日志
docker logs ragflow-minio

# 重启MinIO
docker restart ragflow-minio

# 进入容器
docker exec -it ragflow-minio sh
```

### 管理界面操作
- **访问地址**: http://************:9001
- **默认用户**: minioadmin
- **默认密码**: minioadmin (建议修改)

### 命令行管理 (mc客户端)
```bash
# 安装mc客户端 (如果需要)
docker exec ragflow-minio mc --help

# 配置mc客户端
docker exec ragflow-minio mc alias set local http://localhost:9000 minioadmin minioadmin

# 列出存储桶
docker exec ragflow-minio mc ls local

# 查看存储桶大小
docker exec ragflow-minio mc du local

# 列出文件
docker exec ragflow-minio mc ls local/your-bucket

# 复制文件
docker exec ragflow-minio mc cp local/bucket/file.txt /tmp/backup/
```

### 存储监控
```bash
# 查看存储使用情况
docker exec ragflow-minio mc admin info local

# 查看服务状态
curl -I http://************:9000/minio/health/live

# 查看磁盘使用
docker exec ragflow-minio df -h

# 监控API调用
docker logs ragflow-minio | grep -E "(GET|PUT|POST|DELETE)"
```

### 备份策略
```bash
# 备份存储桶
docker exec ragflow-minio mc mirror local/important-bucket /backup/minio/important-bucket-$(date +%Y%m%d)

# 同步到外部存储 (如果配置了)
# docker exec ragflow-minio mc mirror local/bucket remote/backup-bucket

# 备份配置
docker cp ragflow-minio:/root/.minio ./backup/minio-config-$(date +%Y%m%d)
```

---

## 🔧 基础设施故障排查

### Elasticsearch故障

#### 启动失败
```bash
# 检查容器日志
docker logs ragflow-es-01

# 检查JVM内存设置
docker exec ragflow-es-01 cat /usr/share/elasticsearch/config/jvm.options | grep -E "Xms|Xmx"

# 检查磁盘空间
docker exec ragflow-es-01 df -h

# 重置集群状态 (谨慎操作)
# curl -X PUT "************:1200/_cluster/settings" -H 'Content-Type: application/json' -d'
# {
#   "persistent": {
#     "cluster.routing.allocation.disk.threshold_enabled": false
#   }
# }'
```

#### 性能问题
```bash
# 查看慢查询
curl -X GET "************:1200/_cat/pending_tasks?v"

# 查看线程池状态
curl -X GET "************:1200/_cat/thread_pool?v"

# 优化索引设置
curl -X PUT "************:1200/your_index/_settings" -H 'Content-Type: application/json' -d'
{
  "index": {
    "refresh_interval": "30s",
    "number_of_replicas": 0
  }
}'
```

### MinIO故障

#### 连接问题
```bash
# 测试MinIO API
curl -I http://************:9000

# 测试管理界面
curl -I http://************:9001

# 检查网络连接
docker exec ragflow-minio netstat -tlnp | grep -E "(9000|9001)"
```

#### 存储问题
```bash
# 检查磁盘空间
docker exec ragflow-minio df -h

# 检查文件权限
docker exec ragflow-minio ls -la /data

# 修复存储桶 (如果需要)
docker exec ragflow-minio mc admin heal local/bucket-name
```

---

## 📊 性能优化

### Elasticsearch优化
```bash
# 设置JVM堆内存 (容器启动时)
# -e "ES_JAVA_OPTS=-Xms2g -Xmx2g"

# 优化索引模板
curl -X PUT "************:1200/_index_template/optimized_template" -H 'Content-Type: application/json' -d'
{
  "index_patterns": ["logs-*"],
  "template": {
    "settings": {
      "number_of_shards": 1,
      "number_of_replicas": 0,
      "refresh_interval": "30s"
    }
  }
}'

# 清理旧索引
curl -X DELETE "************:1200/old-logs-*"
```

### MinIO优化
```bash
# 设置环境变量优化性能
# MINIO_API_REQUESTS_MAX=1000
# MINIO_API_REQUESTS_DEADLINE=10s

# 启用压缩
docker exec ragflow-minio mc admin config set local compression enable=on
```

---

## 💾 备份与恢复

### Elasticsearch备份
```bash
# 创建快照仓库
curl -X PUT "************:1200/_snapshot/backup_repo" -H 'Content-Type: application/json' -d'
{
  "type": "fs",
  "settings": {
    "location": "/backup/elasticsearch"
  }
}'

# 创建快照
curl -X PUT "************:1200/_snapshot/backup_repo/snapshot_$(date +%Y%m%d)" -H 'Content-Type: application/json' -d'
{
  "indices": "*",
  "ignore_unavailable": true,
  "include_global_state": false
}'

# 查看快照状态
curl -X GET "************:1200/_snapshot/backup_repo/_all?pretty"
```

### MinIO备份
```bash
# 备份脚本
#!/bin/bash
BACKUP_DIR="/backup/minio/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

# 备份所有存储桶
docker exec ragflow-minio mc mirror local $BACKUP_DIR

# 压缩备份
tar -czf /backup/minio-backup-$(date +%Y%m%d).tar.gz $BACKUP_DIR
```

---

## 🔄 更新维护

### Elasticsearch更新
```bash
# 备份当前版本
docker commit ragflow-es-01 elasticsearch-backup-$(date +%Y%m%d)

# 拉取新版本
docker pull elasticsearch:8.12.0

# 停止当前容器
docker stop ragflow-es-01

# 使用新版本启动
docker run -d --name ragflow-es-01-new \
  -p 1200:9200 \
  --restart unless-stopped \
  elasticsearch:8.12.0

# 验证后替换
docker rm ragflow-es-01
docker rename ragflow-es-01-new ragflow-es-01
```

### MinIO更新
```bash
# 类似的更新流程
docker commit ragflow-minio minio-backup-$(date +%Y%m%d)
docker pull minio/minio:latest
# ... 更新步骤
```

---

## 📅 维护计划

### 日常检查 (每日)
```bash
#!/bin/bash
# 基础设施日常检查

echo "=== 基础设施检查 $(date) ==="

# 检查Elasticsearch
echo "Elasticsearch健康状态:"
curl -s "************:1200/_cluster/health" | jq '.status'

# 检查MinIO
echo "MinIO服务状态:"
curl -I http://************:9000 2>/dev/null && echo "正常" || echo "异常"

# 检查磁盘使用
echo "磁盘使用情况:"
docker exec ragflow-es-01 df -h | grep -E "(Filesystem|/usr/share/elasticsearch/data)"
docker exec ragflow-minio df -h | grep -E "(Filesystem|/data)"
```

### 周期维护 (每周)
- [ ] 清理Elasticsearch旧索引
- [ ] 备份MinIO重要数据
- [ ] 检查存储空间使用
- [ ] 性能监控和优化

### 月度维护 (每月)
- [ ] Elasticsearch索引优化
- [ ] MinIO存储整理
- [ ] 版本更新检查
- [ ] 安全配置审查

---

## 🚨 应急处理

### 快速重启所有基础设施
```bash
# 重启顺序：先启动基础设施，再启动应用
docker restart ragflow-redis
sleep 10
docker restart ragflow-mysql
sleep 10
docker restart ragflow-es-01
sleep 20
docker restart ragflow-minio
sleep 10
docker restart ragflow-server
```

### 灾难恢复
```bash
# 从备份恢复Elasticsearch
curl -X POST "************:1200/_snapshot/backup_repo/snapshot_YYYYMMDD/_restore"

# 从备份恢复MinIO
docker exec ragflow-minio mc mirror /backup/minio/YYYYMMDD local
```

---

## 📞 监控脚本

### 综合监控脚本
```bash
#!/bin/bash
# 基础设施综合监控

echo "=== 基础设施监控报告 $(date) ==="

# Elasticsearch监控
ES_STATUS=$(curl -s "************:1200/_cluster/health" | jq -r '.status')
ES_NODES=$(curl -s "************:1200/_cluster/health" | jq -r '.number_of_nodes')
echo "Elasticsearch: 状态=$ES_STATUS, 节点数=$ES_NODES"

# MinIO监控
MINIO_STATUS=$(curl -I http://************:9000 2>/dev/null && echo "正常" || echo "异常")
echo "MinIO: 状态=$MINIO_STATUS"

# 资源使用
echo "容器资源使用:"
docker stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}" ragflow-es-01 ragflow-minio

# 磁盘空间警告
ES_DISK=$(docker exec ragflow-es-01 df /usr/share/elasticsearch/data | tail -1 | awk '{print $5}' | sed 's/%//')
MINIO_DISK=$(docker exec ragflow-minio df /data | tail -1 | awk '{print $5}' | sed 's/%//')

if [ $ES_DISK -gt 80 ]; then
    echo "警告: Elasticsearch磁盘使用率 ${ES_DISK}% 过高"
fi

if [ $MINIO_DISK -gt 80 ]; then
    echo "警告: MinIO磁盘使用率 ${MINIO_DISK}% 过高"
fi
```

---

## 更新日志
- 2025-08-01: 创建基础设施维护文档
- 2025-08-01: 添加Elasticsearch和MinIO维护指南
- 2025-08-01: 完善监控和故障排查方案
