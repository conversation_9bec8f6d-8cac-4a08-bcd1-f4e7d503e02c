# Open WebUI 维护文档

**应用名称**: Open WebUI  
**版本**: main (latest)  
**服务器**: 110.40.44.80  
**创建时间**: 2025年8月1日  
**最后重启**: 2025年8月1日 09:12

---

## 📋 基本信息

### 应用概述
- **功能**: AI聊天界面，支持多种AI模型集成
- **容器名**: openwebui
- **镜像**: ghcr.io/open-webui/open-webui:main
- **运行状态**: 正常运行 (健康检查: starting → healthy)
- **GitHub**: https://github.com/open-webui/open-webui

### 访问信息
- **主要访问地址**: http://110.40.44.80:3000
- **宝塔反向代理**: openwebui:23cc.cn
- **容器内部端口**: 8080

---

## 🔌 端口配置

| 宿主机端口 | 容器端口 | 用途 | 协议 |
|-----------|---------|------|------|
| 3000 | 8080 | Web界面 | HTTP |

---

## 🛠️ 维护命令

### 基本操作
```bash
# 查看容器状态
docker ps | grep openwebui

# 查看容器详细信息
docker inspect openwebui

# 查看实时日志
docker logs -f openwebui

# 查看最近100行日志
docker logs --tail 100 openwebui

# 重启容器 (注意僵尸进程问题)
docker restart openwebui

# 停止容器
docker stop openwebui

# 启动容器
docker start openwebui
```

### 进入容器调试
```bash
# 进入容器bash
docker exec -it openwebui bash

# 查看容器内进程
docker exec openwebui ps aux

# 查看Python进程详情
docker exec openwebui ps aux | grep python

# 检查uvicorn进程
docker exec openwebui ps aux | grep uvicorn
```

### 健康检查
```bash
# 检查容器健康状态
docker inspect openwebui | grep -A 10 "Health"

# 测试Web服务
curl -I http://110.40.44.80:3000

# 检查响应时间
curl -w "@curl-format.txt" -o /dev/null -s http://110.40.44.80:3000
```

---

## ⚠️ 已知问题

### 僵尸进程问题
- **问题描述**: 容器内的uvicorn进程会产生curl和jq子进程，这些子进程可能变成僵尸进程
- **影响**: 不影响功能，但会占用进程ID
- **解决方案**: 定期重启容器
- **最后解决**: 2025-08-01 09:12 重启容器清理了42个僵尸进程

### 监控僵尸进程
```bash
# 检查系统僵尸进程数量
ps aux | awk '$8 ~ /^Z/' | wc -l

# 查看僵尸进程详情
ps aux | awk '$8 ~ /^Z/ { print "PID: " $2 " PPID: " $3 " CMD: " $11 }'

# 查找Open WebUI相关的僵尸进程
ps -eo pid,ppid,state,comm | awk '$3=="Z" && $4 ~ /(curl|jq)/'
```

### 预防措施
```bash
# 定期重启容器 (建议每周一次)
docker restart openwebui

# 监控脚本 (可添加到crontab)
#!/bin/bash
ZOMBIE_COUNT=$(ps aux | awk '$8 ~ /^Z/' | wc -l)
if [ $ZOMBIE_COUNT -gt 20 ]; then
    echo "$(date): 检测到 $ZOMBIE_COUNT 个僵尸进程，重启 openwebui 容器"
    docker restart openwebui
fi
```

---

## 📊 监控指标

### 资源使用
```bash
# 查看容器资源使用
docker stats openwebui

# 查看容器磁盘使用
docker exec openwebui df -h

# 查看容器内存使用
docker exec openwebui free -h
```

### 性能监控
- **CPU使用率**: 通常在1-10%
- **内存使用**: 约500MB-2GB
- **启动时间**: 约15-30秒
- **健康检查**: 支持自动健康检查

---

## 🔧 故障排查

### 常见问题

#### 1. 容器启动失败
```bash
# 检查容器日志
docker logs openwebui

# 检查端口占用
netstat -tlnp | grep 3000

# 检查镜像是否正常
docker images | grep open-webui
```

#### 2. 访问超时或无响应
```bash
# 检查容器是否运行
docker ps | grep openwebui

# 检查容器健康状态
docker inspect openwebui | grep Health

# 测试内部端口
docker exec openwebui curl -I http://localhost:8080

# 重启容器
docker restart openwebui
```

#### 3. 僵尸进程过多
```bash
# 检查僵尸进程数量
ps aux | awk '$8 ~ /^Z/' | wc -l

# 如果超过20个，重启容器
if [ $(ps aux | awk '$8 ~ /^Z/' | wc -l) -gt 20 ]; then
    docker restart openwebui
fi
```

#### 4. 健康检查失败
```bash
# 查看健康检查日志
docker inspect openwebui | jq '.[0].State.Health'

# 手动执行健康检查
docker exec openwebui curl -f http://localhost:8080/health || exit 1
```

### 日志分析
```bash
# 查找错误日志
docker logs openwebui 2>&1 | grep -i error

# 查找启动相关日志
docker logs openwebui 2>&1 | grep -i "starting\|started\|listening"

# 查找uvicorn相关日志
docker logs openwebui 2>&1 | grep uvicorn
```

---

## 💾 备份与恢复

### 数据备份
```bash
# 备份用户数据 (如果有持久化存储)
docker cp openwebui:/app/backend/data ./backup/openwebui-data-$(date +%Y%m%d)/

# 备份配置文件
docker cp openwebui:/app/backend/config.json ./backup/openwebui-config-$(date +%Y%m%d).json

# 导出容器配置
docker inspect openwebui > openwebui-container-config-$(date +%Y%m%d).json
```

### 配置管理
```bash
# 查看环境变量
docker exec openwebui env | grep -E "(OPENAI|API|MODEL)"

# 查看挂载点
docker inspect openwebui | grep -A 10 "Mounts"
```

---

## 🔄 更新维护

### 版本更新
```bash
# 拉取最新镜像
docker pull ghcr.io/open-webui/open-webui:main

# 停止当前容器
docker stop openwebui

# 备份当前容器
docker commit openwebui openwebui-backup-$(date +%Y%m%d)

# 删除旧容器
docker rm openwebui

# 使用新镜像启动容器 (使用相同参数)
docker run -d --name openwebui \
  -p 3000:8080 \
  --restart unless-stopped \
  ghcr.io/open-webui/open-webui:main
```

### 定期维护任务
```bash
# 每周维护脚本
#!/bin/bash
echo "$(date): 开始 Open WebUI 定期维护"

# 检查僵尸进程
ZOMBIE_COUNT=$(ps aux | awk '$8 ~ /^Z/' | wc -l)
echo "当前僵尸进程数量: $ZOMBIE_COUNT"

# 如果僵尸进程过多，重启容器
if [ $ZOMBIE_COUNT -gt 10 ]; then
    echo "重启 openwebui 容器清理僵尸进程"
    docker restart openwebui
    sleep 30
fi

# 检查容器健康状态
HEALTH=$(docker inspect openwebui | jq -r '.[0].State.Health.Status')
echo "容器健康状态: $HEALTH"

# 清理旧的备份镜像 (保留最近3个)
docker images | grep openwebui-backup | tail -n +4 | awk '{print $3}' | xargs -r docker rmi

echo "$(date): Open WebUI 维护完成"
```

---

## 📈 性能优化

### 资源限制
```bash
# 设置内存限制 (如果需要)
docker update --memory=2g openwebui

# 设置CPU限制
docker update --cpus=2 openwebui
```

### 监控脚本
```bash
# 性能监控脚本
#!/bin/bash
echo "=== Open WebUI 性能监控 ==="
echo "时间: $(date)"
echo "容器状态: $(docker ps --format 'table {{.Status}}' | grep openwebui)"
echo "内存使用: $(docker stats --no-stream --format 'table {{.MemUsage}}' openwebui)"
echo "CPU使用: $(docker stats --no-stream --format 'table {{.CPUPerc}}' openwebui)"
echo "僵尸进程: $(ps aux | awk '$8 ~ /^Z/' | wc -l) 个"
```

---

## 🚨 应急处理

### 快速重启
```bash
# 快速重启命令
docker restart openwebui && sleep 30 && curl -I http://110.40.44.80:3000
```

### 应急回滚
```bash
# 如果更新后出现问题，回滚到备份版本
docker stop openwebui
docker rm openwebui
docker run -d --name openwebui [原始参数] openwebui-backup-YYYYMMDD
```

---

## 📞 维护计划

### 日常检查 (每日)
- 检查容器运行状态
- 查看错误日志
- 监控僵尸进程数量

### 周期维护 (每周)
- 重启容器清理僵尸进程
- 检查资源使用情况
- 清理旧日志文件

### 月度维护 (每月)
- 检查版本更新
- 备份重要配置
- 性能评估和优化

---

## 更新日志
- 2025-08-01: 创建Open WebUI维护文档
- 2025-08-01: 记录僵尸进程问题和解决方案
- 2025-08-01: 添加定期维护脚本和监控方案
