# 数据库维护文档

**服务器**: ************  
**创建时间**: 2025年8月1日

---

## 📋 数据库概览

### MySQL数据库实例

#### 1. RAG Flow MySQL
- **容器名**: ragflow-mysql
- **镜像**: mysql:8.0.39
- **端口**: 5455:3306
- **用途**: RAG Flow AI文档问答系统数据存储
- **状态**: 正常运行

#### 2. PlayEdu MySQL
- **容器名**: playedu-mysql-1
- **镜像**: playedu-mysql (自定义)
- **端口**: 23307:3306
- **用途**: PlayEdu教育平台数据存储
- **状态**: 正常运行

### Redis缓存数据库
- **容器名**: ragflow-redis
- **镜像**: valkey/valkey:8
- **端口**: 6380:6379
- **用途**: RAG Flow缓存和会话存储
- **状态**: 正常运行

---

## 🔌 连接信息

### MySQL连接
```bash
# RAG Flow MySQL
mysql -h ************ -P 5455 -u root -p

# PlayEdu MySQL
mysql -h ************ -P 23307 -u root -p

# 从容器内连接
docker exec -it ragflow-mysql mysql -u root -p
docker exec -it playedu-mysql-1 mysql -u root -p
```

### Redis连接
```bash
# Redis连接
redis-cli -h ************ -p 6380

# 从容器内连接
docker exec -it ragflow-redis redis-cli
```

---

## 🛠️ 基本维护命令

### MySQL维护

#### 容器管理
```bash
# 查看MySQL容器状态
docker ps | grep mysql

# 查看MySQL日志
docker logs ragflow-mysql
docker logs playedu-mysql-1

# 重启MySQL容器
docker restart ragflow-mysql
docker restart playedu-mysql-1

# 进入MySQL容器
docker exec -it ragflow-mysql bash
docker exec -it playedu-mysql-1 bash
```

#### 数据库操作
```bash
# 查看数据库列表
mysql -h ************ -P 5455 -u root -p -e "SHOW DATABASES;"
mysql -h ************ -P 23307 -u root -p -e "SHOW DATABASES;"

# 查看数据库大小
mysql -h ************ -P 5455 -u root -p -e "
SELECT 
    table_schema AS 'Database',
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)'
FROM information_schema.tables 
GROUP BY table_schema;"

# 查看表状态
mysql -h ************ -P 5455 -u root -p -e "
USE ragflow;
SHOW TABLE STATUS;"
```

### Redis维护

#### 基本操作
```bash
# 查看Redis信息
docker exec ragflow-redis redis-cli info

# 查看内存使用
docker exec ragflow-redis redis-cli info memory

# 查看连接数
docker exec ragflow-redis redis-cli info clients

# 查看键数量
docker exec ragflow-redis redis-cli dbsize

# 清理过期键
docker exec ragflow-redis redis-cli --scan --pattern "*" | head -10
```

---

## 📊 监控指标

### MySQL监控
```bash
# 查看MySQL进程列表
mysql -h ************ -P 5455 -u root -p -e "SHOW PROCESSLIST;"

# 查看MySQL状态
mysql -h ************ -P 5455 -u root -p -e "SHOW STATUS LIKE 'Threads%';"

# 查看慢查询
mysql -h ************ -P 5455 -u root -p -e "SHOW VARIABLES LIKE 'slow_query%';"

# 查看InnoDB状态
mysql -h ************ -P 5455 -u root -p -e "SHOW ENGINE INNODB STATUS\G"
```

### Redis监控
```bash
# Redis性能监控
docker exec ragflow-redis redis-cli info stats

# 查看Redis配置
docker exec ragflow-redis redis-cli config get "*"

# 监控Redis命令
docker exec ragflow-redis redis-cli monitor
```

### 资源使用监控
```bash
# 查看数据库容器资源使用
docker stats ragflow-mysql playedu-mysql-1 ragflow-redis

# 查看磁盘使用
docker exec ragflow-mysql df -h
docker exec playedu-mysql-1 df -h
```

---

## 💾 备份策略

### MySQL备份

#### 自动备份脚本
```bash
#!/bin/bash
# MySQL自动备份脚本

BACKUP_DIR="/backup/mysql"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份RAG Flow数据库
echo "$(date): 开始备份RAG Flow数据库"
docker exec ragflow-mysql mysqldump -u root -p --single-transaction --routines --triggers ragflow > $BACKUP_DIR/ragflow_$DATE.sql

# 备份PlayEdu数据库
echo "$(date): 开始备份PlayEdu数据库"
docker exec playedu-mysql-1 mysqldump -u root -p --single-transaction --routines --triggers playedu > $BACKUP_DIR/playedu_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/ragflow_$DATE.sql
gzip $BACKUP_DIR/playedu_$DATE.sql

# 清理7天前的备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete

echo "$(date): 数据库备份完成"
```

#### 手动备份
```bash
# RAG Flow完整备份
docker exec ragflow-mysql mysqldump -u root -p --all-databases --single-transaction --routines --triggers > ragflow_full_backup_$(date +%Y%m%d).sql

# PlayEdu完整备份
docker exec playedu-mysql-1 mysqldump -u root -p --all-databases --single-transaction --routines --triggers > playedu_full_backup_$(date +%Y%m%d).sql

# 仅备份数据 (不包含结构)
docker exec ragflow-mysql mysqldump -u root -p --no-create-info --single-transaction ragflow > ragflow_data_only_$(date +%Y%m%d).sql
```

### Redis备份
```bash
# Redis数据备份
docker exec ragflow-redis redis-cli BGSAVE

# 复制RDB文件
docker cp ragflow-redis:/data/dump.rdb ./backup/redis_backup_$(date +%Y%m%d).rdb

# Redis配置备份
docker exec ragflow-redis redis-cli config get "*" > redis_config_$(date +%Y%m%d).txt
```

---

## 🔄 恢复操作

### MySQL恢复
```bash
# 恢复RAG Flow数据库
mysql -h ************ -P 5455 -u root -p < ragflow_backup_YYYYMMDD.sql

# 恢复PlayEdu数据库
mysql -h ************ -P 23307 -u root -p < playedu_backup_YYYYMMDD.sql

# 恢复特定表
mysql -h ************ -P 5455 -u root -p ragflow < table_backup.sql
```

### Redis恢复
```bash
# 停止Redis容器
docker stop ragflow-redis

# 替换RDB文件
docker cp redis_backup_YYYYMMDD.rdb ragflow-redis:/data/dump.rdb

# 启动Redis容器
docker start ragflow-redis
```

---

## 🔧 故障排查

### MySQL故障排查

#### 连接问题
```bash
# 测试连接
telnet ************ 5455
telnet ************ 23307

# 检查端口监听
docker exec ragflow-mysql netstat -tlnp | grep 3306

# 查看错误日志
docker logs ragflow-mysql | grep -i error
docker logs playedu-mysql-1 | grep -i error
```

#### 性能问题
```bash
# 查看慢查询日志
docker exec ragflow-mysql mysql -u root -p -e "
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;
SHOW VARIABLES LIKE 'slow_query%';"

# 查看锁等待
mysql -h ************ -P 5455 -u root -p -e "
SELECT * FROM information_schema.INNODB_LOCKS;
SELECT * FROM information_schema.INNODB_LOCK_WAITS;"

# 查看表大小
mysql -h ************ -P 5455 -u root -p -e "
SELECT 
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.TABLES 
WHERE table_schema = 'ragflow'
ORDER BY (data_length + index_length) DESC;"
```

### Redis故障排查
```bash
# 检查Redis状态
docker exec ragflow-redis redis-cli ping

# 查看Redis日志
docker logs ragflow-redis

# 检查内存使用
docker exec ragflow-redis redis-cli info memory | grep used_memory

# 查看慢查询
docker exec ragflow-redis redis-cli slowlog get 10
```

---

## 🔒 安全维护

### MySQL安全
```bash
# 查看用户权限
mysql -h ************ -P 5455 -u root -p -e "
SELECT User, Host, authentication_string FROM mysql.user;
SHOW GRANTS FOR 'root'@'%';"

# 更改root密码
mysql -h ************ -P 5455 -u root -p -e "
ALTER USER 'root'@'%' IDENTIFIED BY 'new_password';"

# 删除匿名用户
mysql -h ************ -P 5455 -u root -p -e "
DELETE FROM mysql.user WHERE User='';
FLUSH PRIVILEGES;"
```

### Redis安全
```bash
# 设置Redis密码
docker exec ragflow-redis redis-cli config set requirepass "your_password"

# 禁用危险命令
docker exec ragflow-redis redis-cli config set rename-command FLUSHDB ""
docker exec ragflow-redis redis-cli config set rename-command FLUSHALL ""
```

---

## 📈 性能优化

### MySQL优化
```bash
# 查看MySQL配置
mysql -h ************ -P 5455 -u root -p -e "SHOW VARIABLES LIKE 'innodb%';"

# 优化表
mysql -h ************ -P 5455 -u root -p -e "
USE ragflow;
OPTIMIZE TABLE documents, chunks, conversations;"

# 分析表
mysql -h ************ -P 5455 -u root -p -e "
USE ragflow;
ANALYZE TABLE documents, chunks, conversations;"

# 查看索引使用情况
mysql -h ************ -P 5455 -u root -p -e "
SELECT 
    TABLE_SCHEMA,
    TABLE_NAME,
    INDEX_NAME,
    CARDINALITY
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = 'ragflow';"
```

### Redis优化
```bash
# 设置内存策略
docker exec ragflow-redis redis-cli config set maxmemory-policy allkeys-lru

# 启用持久化
docker exec ragflow-redis redis-cli config set save "900 1 300 10 60 10000"

# 查看键过期情况
docker exec ragflow-redis redis-cli info keyspace
```

---

## 📅 维护计划

### 日常检查 (每日)
```bash
#!/bin/bash
# 数据库日常检查脚本

echo "=== 数据库日常检查 $(date) ==="

# 检查MySQL容器状态
echo "MySQL容器状态:"
docker ps | grep mysql

# 检查Redis容器状态
echo "Redis容器状态:"
docker ps | grep redis

# 测试数据库连接
echo "测试RAG Flow MySQL连接:"
mysql -h ************ -P 5455 -u root -p -e "SELECT 1;" && echo "连接正常" || echo "连接失败"

echo "测试PlayEdu MySQL连接:"
mysql -h ************ -P 23307 -u root -p -e "SELECT 1;" && echo "连接正常" || echo "连接失败"

echo "测试Redis连接:"
docker exec ragflow-redis redis-cli ping && echo "连接正常" || echo "连接失败"

# 检查磁盘空间
echo "数据库磁盘使用:"
docker exec ragflow-mysql df -h | grep -E "(Filesystem|/var/lib/mysql)"
docker exec playedu-mysql-1 df -h | grep -E "(Filesystem|/var/lib/mysql)"
```

### 周期维护 (每周)
- [ ] 执行数据库备份
- [ ] 清理过期日志
- [ ] 优化数据库表
- [ ] 检查慢查询日志

### 月度维护 (每月)
- [ ] 分析数据库性能
- [ ] 更新数据库统计信息
- [ ] 检查索引使用情况
- [ ] 安全审计

---

## 🚨 应急处理

### 数据库无法启动
```bash
# 检查容器状态
docker ps -a | grep mysql

# 查看启动日志
docker logs ragflow-mysql
docker logs playedu-mysql-1

# 尝试重启
docker restart ragflow-mysql
docker restart playedu-mysql-1

# 如果仍然失败，检查数据目录
docker exec ragflow-mysql ls -la /var/lib/mysql/
```

### 数据损坏恢复
```bash
# MySQL修复
docker exec ragflow-mysql mysqlcheck -u root -p --auto-repair --all-databases

# InnoDB恢复
docker exec ragflow-mysql mysql -u root -p -e "SET GLOBAL innodb_force_recovery = 1;"
```

---

## 更新日志
- 2025-08-01: 创建数据库维护文档
- 2025-08-01: 添加MySQL和Redis维护指南
- 2025-08-01: 完善备份恢复和故障排查方案
