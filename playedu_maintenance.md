# PlayEdu 教育平台维护文档

**应用名称**: PlayEdu  
**版本**: 2.0 (Light版)  
**服务器**: 110.40.44.80  
**创建时间**: 2025年8月1日

---

## 📋 基本信息

### 应用概述
- **功能**: 在线教育平台，支持课程管理、学员管理、考试系统
- **容器名**: playedu-playedu-1
- **镜像**: registry.cn-hangzhou.aliyuncs.com/playedu/light:2.0
- **运行状态**: 正常运行
- **官网**: https://playedu.xyz/
- **GitHub**: https://github.com/PlayEdu/PlayEdu

### 访问信息
- **PC端访问**: http://110.40.44.80:9700
- **管理后台**: 通过宝塔反向代理 edu-admin:23cc.cn
- **宝塔反向代理**: edu:23cc.cn (PC端), edu-admin:23cc.cn (管理后台)
- **容器内部端口**: 9898

---

## 🔌 端口配置

| 宿主机端口 | 容器端口 | 用途 | 协议 |
|-----------|---------|------|------|
| 9700 | 9898 | PC端Web界面 | HTTP |
| 9800 | 9800 | 附加服务1 | HTTP |
| 9801 | 9801 | 附加服务2 | HTTP |
| 9900 | 9900 | 管理接口 | HTTP |

---

## 🗄️ 依赖服务

### 数据库服务
- **MySQL**: playedu-mysql-1
  - 端口: 23307:3306
  - 镜像: playedu-mysql
  - 用途: 用户数据、课程内容、学习记录存储
  - 健康状态: 正常

### 数据库连接信息
```bash
# 连接PlayEdu数据库
mysql -h 110.40.44.80 -P 23307 -u root -p

# 查看数据库列表
mysql -h 110.40.44.80 -P 23307 -u root -p -e "SHOW DATABASES;"

# 查看PlayEdu相关表
mysql -h 110.40.44.80 -P 23307 -u root -p -e "USE playedu; SHOW TABLES;"
```

---

## 🛠️ 维护命令

### 基本操作
```bash
# 查看容器状态
docker ps | grep playedu

# 查看容器详细信息
docker inspect playedu-playedu-1

# 查看实时日志
docker logs -f playedu-playedu-1

# 查看最近100行日志
docker logs --tail 100 playedu-playedu-1

# 重启容器
docker restart playedu-playedu-1

# 停止容器
docker stop playedu-playedu-1

# 启动容器
docker start playedu-playedu-1
```

### 进入容器调试
```bash
# 进入容器bash
docker exec -it playedu-playedu-1 bash

# 查看容器内进程
docker exec playedu-playedu-1 ps aux

# 查看Java进程
docker exec playedu-playedu-1 ps aux | grep java

# 检查应用配置
docker exec playedu-playedu-1 cat /app/application.yml
```

### 健康检查
```bash
# 测试PC端Web服务
curl -I http://110.40.44.80:9700

# 测试管理接口
curl -I http://110.40.44.80:9900

# 检查数据库连接
mysql -h 110.40.44.80 -P 23307 -u root -p -e "SELECT 1;"

# 测试响应时间
curl -w "%{time_total}" -o /dev/null -s http://110.40.44.80:9700
```

---

## 📊 监控指标

### 资源使用
```bash
# 查看容器资源使用
docker stats playedu-playedu-1

# 查看容器磁盘使用
docker exec playedu-playedu-1 df -h

# 查看Java内存使用
docker exec playedu-playedu-1 java -XX:+PrintFlagsFinal -version | grep HeapSize
```

### 性能监控
- **CPU使用率**: 通常在2-15%
- **内存使用**: 约1-3GB (Java应用)
- **启动时间**: 约30-60秒
- **数据库连接**: 监控连接池状态

---

## 🎓 教育平台管理

### 用户管理
```bash
# 查看用户统计
mysql -h 110.40.44.80 -P 23307 -u root -p -e "
USE playedu; 
SELECT COUNT(*) as total_users FROM users;
SELECT COUNT(*) as active_users FROM users WHERE status = 1;
"

# 查看最近注册用户
mysql -h 110.40.44.80 -P 23307 -u root -p -e "
USE playedu; 
SELECT id, name, email, created_at FROM users ORDER BY created_at DESC LIMIT 10;
"
```

### 课程管理
```bash
# 查看课程统计
mysql -h 110.40.44.80 -P 23307 -u root -p -e "
USE playedu; 
SELECT COUNT(*) as total_courses FROM courses;
SELECT COUNT(*) as published_courses FROM courses WHERE status = 1;
"

# 查看热门课程
mysql -h 110.40.44.80 -P 23307 -u root -p -e "
USE playedu; 
SELECT id, title, view_count FROM courses ORDER BY view_count DESC LIMIT 10;
"
```

### 学习记录
```bash
# 查看学习统计
mysql -h 110.40.44.80 -P 23307 -u root -p -e "
USE playedu; 
SELECT COUNT(*) as total_records FROM user_course_records;
SELECT COUNT(*) as completed_courses FROM user_course_records WHERE is_finished = 1;
"
```

---

## 🔧 故障排查

### 常见问题

#### 1. 容器启动失败
```bash
# 检查容器日志
docker logs playedu-playedu-1

# 检查端口占用
netstat -tlnp | grep -E "(9700|9800|9801|9900)"

# 检查数据库连接
mysql -h 110.40.44.80 -P 23307 -u root -p -e "SELECT 1;"

# 检查镜像状态
docker images | grep playedu
```

#### 2. 访问超时或无响应
```bash
# 检查容器状态
docker ps | grep playedu

# 测试各个端口
curl -I http://110.40.44.80:9700  # PC端
curl -I http://110.40.44.80:9900  # 管理接口

# 重启容器
docker restart playedu-playedu-1
```

#### 3. 数据库连接问题
```bash
# 测试数据库连接
mysql -h 110.40.44.80 -P 23307 -u root -p

# 检查数据库容器状态
docker ps | grep playedu-mysql

# 重启数据库容器
docker restart playedu-mysql-1
```

#### 4. 登录或功能异常
```bash
# 检查应用日志中的错误
docker logs playedu-playedu-1 | grep -i "error\|exception"

# 检查数据库表结构
mysql -h 110.40.44.80 -P 23307 -u root -p -e "USE playedu; DESCRIBE users;"

# 清理缓存 (如果有Redis)
# docker exec redis-container redis-cli FLUSHALL
```

### 日志分析
```bash
# 查找错误日志
docker logs playedu-playedu-1 2>&1 | grep -i error

# 查找SQL相关日志
docker logs playedu-playedu-1 2>&1 | grep -i "sql\|mysql"

# 查找用户操作日志
docker logs playedu-playedu-1 2>&1 | grep -i "login\|user"

# 查找特定时间段日志
docker logs playedu-playedu-1 --since="2025-08-01T09:00:00" --until="2025-08-01T10:00:00"
```

---

## 💾 备份与恢复

### 数据库备份
```bash
# 创建备份目录
mkdir -p ./backup/playedu-$(date +%Y%m%d)

# 备份PlayEdu数据库
docker exec playedu-mysql-1 mysqldump -u root -p playedu > ./backup/playedu-$(date +%Y%m%d)/playedu-db.sql

# 备份所有数据库
docker exec playedu-mysql-1 mysqldump -u root -p --all-databases > ./backup/playedu-$(date +%Y%m%d)/all-databases.sql

# 压缩备份文件
tar -czf ./backup/playedu-backup-$(date +%Y%m%d).tar.gz ./backup/playedu-$(date +%Y%m%d)/
```

### 应用配置备份
```bash
# 备份应用配置
docker cp playedu-playedu-1:/app/application.yml ./backup/playedu-$(date +%Y%m%d)/

# 备份上传文件 (如果有持久化存储)
docker cp playedu-playedu-1:/app/uploads ./backup/playedu-$(date +%Y%m%d)/uploads/

# 导出容器配置
docker inspect playedu-playedu-1 > ./backup/playedu-$(date +%Y%m%d)/container-config.json
```

### 恢复操作
```bash
# 恢复数据库
mysql -h 110.40.44.80 -P 23307 -u root -p < ./backup/playedu-YYYYMMDD/playedu-db.sql

# 恢复配置文件
docker cp ./backup/playedu-YYYYMMDD/application.yml playedu-playedu-1:/app/

# 重启应用
docker restart playedu-playedu-1
```

---

## 🔄 更新维护

### 版本更新
```bash
# 备份当前版本
docker commit playedu-playedu-1 playedu-backup-$(date +%Y%m%d)

# 拉取最新镜像
docker pull registry.cn-hangzhou.aliyuncs.com/playedu/light:latest

# 停止当前容器
docker stop playedu-playedu-1

# 备份数据库
docker exec playedu-mysql-1 mysqldump -u root -p playedu > playedu-pre-update-$(date +%Y%m%d).sql

# 使用新镜像启动容器
docker run -d --name playedu-playedu-1-new \
  -p 9700:9898 \
  -p 9800:9800 \
  -p 9801:9801 \
  -p 9900:9900 \
  --restart unless-stopped \
  registry.cn-hangzhou.aliyuncs.com/playedu/light:latest

# 验证新版本正常后替换
docker rm playedu-playedu-1
docker rename playedu-playedu-1-new playedu-playedu-1
```

### 定期维护脚本
```bash
#!/bin/bash
# PlayEdu 定期维护脚本

echo "$(date): 开始 PlayEdu 定期维护"

# 检查容器状态
STATUS=$(docker inspect playedu-playedu-1 | jq -r '.[0].State.Status')
echo "应用容器状态: $STATUS"

DB_STATUS=$(docker inspect playedu-mysql-1 | jq -r '.[0].State.Status')
echo "数据库容器状态: $DB_STATUS"

# 检查服务响应
if curl -f -s http://110.40.44.80:9700 > /dev/null; then
    echo "PlayEdu PC端响应正常"
else
    echo "PlayEdu PC端无响应，重启容器"
    docker restart playedu-playedu-1
    sleep 30
fi

# 检查数据库连接
if mysql -h 110.40.44.80 -P 23307 -u root -p -e "SELECT 1;" > /dev/null 2>&1; then
    echo "数据库连接正常"
else
    echo "数据库连接异常，检查数据库容器"
    docker restart playedu-mysql-1
    sleep 20
fi

# 数据库备份 (每周执行)
if [ $(date +%u) -eq 1 ]; then
    echo "执行每周数据库备份"
    mkdir -p ./backup/playedu-weekly-$(date +%Y%m%d)
    docker exec playedu-mysql-1 mysqldump -u root -p playedu > ./backup/playedu-weekly-$(date +%Y%m%d)/playedu-db.sql
fi

# 清理旧备份 (保留最近4周)
find ./backup -name "playedu-weekly-*" -type d -mtime +28 -exec rm -rf {} \;

echo "$(date): PlayEdu 维护完成"
```

---

## 📈 性能优化

### Java应用优化
```bash
# 查看JVM参数
docker exec playedu-playedu-1 java -XX:+PrintFlagsFinal -version | grep -E "(HeapSize|MetaspaceSize)"

# 设置JVM内存参数 (在容器启动时)
# -Xms1g -Xmx2g -XX:MetaspaceSize=256m
```

### 数据库优化
```bash
# 查看数据库性能
mysql -h 110.40.44.80 -P 23307 -u root -p -e "SHOW PROCESSLIST;"

# 查看慢查询
mysql -h 110.40.44.80 -P 23307 -u root -p -e "SHOW VARIABLES LIKE 'slow_query%';"

# 优化表
mysql -h 110.40.44.80 -P 23307 -u root -p -e "USE playedu; OPTIMIZE TABLE users, courses, user_course_records;"
```

---

## 🚨 应急处理

### 快速重启
```bash
# 重启应用和数据库
docker restart playedu-mysql-1 && sleep 10 && docker restart playedu-playedu-1 && sleep 30 && curl -I http://110.40.44.80:9700
```

### 应急回滚
```bash
# 回滚到备份版本
docker stop playedu-playedu-1
docker rm playedu-playedu-1
docker run -d --name playedu-playedu-1 [原始参数] playedu-backup-YYYYMMDD
```

---

## 📞 维护计划

### 日常检查 (每日)
- [ ] 检查容器运行状态
- [ ] 验证PC端和管理后台访问
- [ ] 检查数据库连接
- [ ] 查看错误日志

### 周期维护 (每周)
- [ ] 备份数据库
- [ ] 检查用户活跃度
- [ ] 清理临时文件
- [ ] 性能监控

### 月度维护 (每月)
- [ ] 检查版本更新
- [ ] 数据库优化
- [ ] 备份验证
- [ ] 安全检查

---

## 更新日志
- 2025-08-01: 创建 PlayEdu 维护文档
- 2025-08-01: 添加教育平台管理和数据库操作指南
- 2025-08-01: 完善故障排查和性能优化方案
