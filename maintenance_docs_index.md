# 服务器维护文档索引

**服务器**: 110.40.44.80  
**创建时间**: 2025年8月1日  
**文档管理员**: 系统管理员

---

## 📚 文档结构

### 🏠 主要文档
1. **[server_info_document.md](./server_info_document.md)** - 服务器系统信息总览
   - 硬件配置、操作系统、网络配置
   - 宝塔面板配置、Docker环境
   - 系统资源使用情况

2. **[docker_applications_maintenance.md](./docker_applications_maintenance.md)** - Docker应用维护总览
   - 所有Docker应用概览表
   - 基本维护命令和最佳实践

### 🐳 应用专项文档
3. **[ragflow_maintenance.md](./ragflow_maintenance.md)** - RAG Flow维护文档
   - AI文档问答系统维护指南
   - 依赖服务管理、故障排查

4. **[openwebui_maintenance.md](./openwebui_maintenance.md)** - Open WebUI维护文档
   - AI聊天界面维护指南
   - 僵尸进程问题解决方案

5. **[n8n_maintenance.md](./n8n_maintenance.md)** - n8n工作流维护文档
   - 工作流自动化平台维护指南
   - 工作流备份恢复和性能优化

6. **[playedu_maintenance.md](./playedu_maintenance.md)** - PlayEdu教育平台维护文档
   - 在线教育平台维护指南
   - 用户管理、课程管理和数据库操作

7. **[database_maintenance.md](./database_maintenance.md)** - 数据库维护文档
   - MySQL和Redis数据库维护指南
   - 备份恢复、性能优化和故障排查

8. **[infrastructure_maintenance.md](./infrastructure_maintenance.md)** - 基础设施维护文档
   - Elasticsearch和MinIO维护指南
   - 搜索引擎和对象存储管理

---

## 🔍 快速查找指南

### 按问题类型查找

#### 🚨 紧急故障
- **服务无法访问**: 查看对应应用的维护文档 → 故障排查章节
- **系统资源不足**: [server_info_document.md](./server_info_document.md) → 系统资源使用情况
- **容器无法启动**: [docker_applications_maintenance.md](./docker_applications_maintenance.md) → 维护最佳实践

#### 🔧 日常维护
- **定期检查**: 各应用维护文档 → 监控指标章节
- **备份操作**: 各应用维护文档 → 备份与恢复章节
- **版本更新**: 各应用维护文档 → 更新维护章节

#### 📊 监控和优化
- **性能监控**: 各应用维护文档 → 监控指标章节
- **资源优化**: [server_info_document.md](./server_info_document.md) → 系统资源使用情况
- **安全配置**: [server_info_document.md](./server_info_document.md) → 网络安全配置

### 按应用查找

#### RAG Flow 相关
- **基本信息**: [ragflow_maintenance.md](./ragflow_maintenance.md)
- **依赖服务**: MySQL, Elasticsearch, MinIO, Redis
- **常见问题**: 启动慢、内存占用高、搜索功能异常

#### Open WebUI 相关
- **基本信息**: [openwebui_maintenance.md](./openwebui_maintenance.md)
- **特殊问题**: 僵尸进程问题
- **维护频率**: 建议每周重启一次

#### n8n 相关
- **基本信息**: [n8n_maintenance.md](./n8n_maintenance.md)
- **主要功能**: 工作流自动化和API集成
- **维护重点**: 工作流备份、性能监控

#### PlayEdu 相关
- **基本信息**: [playedu_maintenance.md](./playedu_maintenance.md)
- **主要功能**: 在线教育平台和课程管理
- **维护重点**: 数据库管理、用户数据备份

#### 数据库相关
- **基本信息**: [database_maintenance.md](./database_maintenance.md)
- **包含服务**: MySQL (RAG Flow + PlayEdu), Redis
- **维护重点**: 数据备份、性能监控、故障恢复

#### 基础设施相关
- **基本信息**: [infrastructure_maintenance.md](./infrastructure_maintenance.md)
- **包含服务**: Elasticsearch, MinIO
- **维护重点**: 搜索索引管理、对象存储维护

---

## 📅 维护计划

### 每日检查清单
- [ ] 检查所有Docker容器状态: `docker ps`
- [ ] 查看系统负载: `uptime`
- [ ] 检查磁盘空间: `df -h`
- [ ] 查看重要服务日志
- [ ] 监控僵尸进程数量: `ps aux | awk '$8 ~ /^Z/' | wc -l`

### 每周维护清单
- [ ] 重启Open WebUI容器清理僵尸进程
- [ ] 检查所有应用的错误日志
- [ ] 清理Docker无用镜像: `docker system prune`
- [ ] 备份重要配置文件
- [ ] 更新维护文档

### 每月维护清单
- [ ] 检查应用版本更新
- [ ] 备份数据库数据
- [ ] 性能评估和优化
- [ ] 安全检查和更新
- [ ] 文档更新和整理

---

## 🛠️ 常用命令速查

### Docker管理
```bash
# 查看所有容器状态
docker ps

# 查看容器资源使用
docker stats

# 重启所有容器
docker restart $(docker ps -q)

# 清理系统
docker system prune -f
```

### 系统监控
```bash
# 系统负载
uptime

# 内存使用
free -h

# 磁盘使用
df -h

# 进程统计
ps aux | awk 'BEGIN {r=0;s=0;z=0} $8=="R" {r++} $8~/^S/ {s++} $8=="Z" {z++} END {print "运行:"r" 睡眠:"s" 僵尸:"z}'
```

### 网络检查
```bash
# 检查端口监听
netstat -tlnp

# 测试服务连通性
curl -I http://110.40.44.80:3000  # Open WebUI
curl -I http://110.40.44.80:3010  # RAG Flow
curl -I http://110.40.44.80:5678  # n8n
curl -I http://110.40.44.80:9700  # PlayEdu
```

---

## 📞 应急联系信息

### 重要端口列表
- **宝塔面板**: 34268
- **Open WebUI**: 3000
- **RAG Flow**: 3010
- **n8n**: 5678
- **PlayEdu**: 9700
- **SSH**: 22
- **HTTP**: 80
- **HTTPS**: 443

### 快速重启命令
```bash
# 重启宝塔面板
systemctl restart bt

# 重启所有Docker容器
docker restart $(docker ps -q)

# 重启网络服务
systemctl restart networking
```

---

## 📝 文档维护

### 更新规则
1. **每次重大变更后更新相关文档**
2. **每月检查文档准确性**
3. **记录所有问题解决方案**
4. **保持文档版本同步**

### 文档版本控制
- 使用Git管理文档版本
- 重要变更需要备份
- 定期归档旧版本

### 贡献指南
1. 发现问题时及时记录
2. 解决问题后更新文档
3. 新增应用时创建对应文档
4. 定期review和优化文档结构

---

## 🔄 文档状态

| 文档名称 | 状态 | 最后更新 | 下次review |
|---------|------|----------|-----------|
| server_info_document.md | ✅ 完成 | 2025-08-01 | 2025-09-01 |
| docker_applications_maintenance.md | ✅ 完成 | 2025-08-01 | 2025-09-01 |
| ragflow_maintenance.md | ✅ 完成 | 2025-08-01 | 2025-09-01 |
| openwebui_maintenance.md | ✅ 完成 | 2025-08-01 | 2025-09-01 |
| n8n_maintenance.md | ✅ 完成 | 2025-08-01 | 2025-09-01 |
| playedu_maintenance.md | ✅ 完成 | 2025-08-01 | 2025-09-01 |
| database_maintenance.md | ✅ 完成 | 2025-08-01 | 2025-09-01 |
| infrastructure_maintenance.md | ✅ 完成 | 2025-08-01 | 2025-09-01 |

---

## 更新日志
- 2025-08-01: 创建维护文档索引
- 2025-08-01: 完成主要应用维护文档 (RAG Flow, Open WebUI)
- 2025-08-01: 完成n8n工作流维护文档
- 2025-08-01: 完成PlayEdu教育平台维护文档
- 2025-08-01: 完成数据库维护文档 (MySQL, Redis)
- 2025-08-01: 完成基础设施维护文档 (Elasticsearch, MinIO)
- 2025-08-01: 建立完整的文档管理体系
