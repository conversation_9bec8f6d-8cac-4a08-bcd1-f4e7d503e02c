# RAG Flow 维护文档

**应用名称**: RAG Flow  
**版本**: v0.19.1-slim  
**服务器**: 110.40.44.80  
**创建时间**: 2025年8月1日

---

## 📋 基本信息

### 应用概述
- **功能**: AI文档问答系统，支持知识库构建和智能问答
- **容器名**: ragflow-server
- **镜像**: infiniflow/ragflow:v0.19.1-slim
- **运行状态**: 正常运行 (Up 2 days)
- **健康检查**: 支持

### 访问信息
- **主要访问地址**: http://110.40.44.80:3010
- **HTTPS访问**: https://110.40.44.80:8443
- **宝塔反向代理**: ragflow:23cc.cn
- **API接口**: http://110.40.44.80:5679

---

## 🔌 端口配置

| 宿主机端口 | 容器端口 | 用途 | 协议 |
|-----------|---------|------|------|
| 3010 | 80 | Web主界面 | HTTP |
| 5679 | 5679 | API服务 | HTTP |
| 5680 | 5678 | 内部通信 | HTTP |
| 8443 | 443 | HTTPS访问 | HTTPS |
| 9380 | 9380 | 管理接口 | HTTP |
| 9382 | 9382 | MCP服务 | HTTP |

---

## 🗄️ 依赖服务

### 数据库服务
- **MySQL**: ragflow-mysql
  - 端口: 5455:3306
  - 用途: 用户数据、配置信息存储
  - 健康状态: 正常

### 搜索引擎
- **Elasticsearch**: ragflow-es-01
  - 端口: 1200:9200
  - 版本: 8.11.3
  - 用途: 文档索引和全文搜索
  - 健康状态: 正常

### 对象存储
- **MinIO**: ragflow-minio
  - 端口: 9000-9001:9000-9001
  - 用途: 文档文件存储
  - 管理界面: http://110.40.44.80:9001
  - 健康状态: 正常

### 缓存服务
- **Redis**: ragflow-redis
  - 端口: 6380:6379
  - 镜像: valkey/valkey:8
  - 用途: 会话缓存、临时数据
  - 健康状态: 正常

---

## 🛠️ 维护命令

### 基本操作
```bash
# 查看容器状态
docker ps | grep ragflow-server

# 查看容器详细信息
docker inspect ragflow-server

# 查看实时日志
docker logs -f ragflow-server

# 查看最近100行日志
docker logs --tail 100 ragflow-server

# 重启容器
docker restart ragflow-server

# 停止容器
docker stop ragflow-server

# 启动容器
docker start ragflow-server
```

### 进入容器调试
```bash
# 进入容器bash
docker exec -it ragflow-server bash

# 以root用户进入
docker exec -it -u root ragflow-server bash

# 查看容器内进程
docker exec ragflow-server ps aux

# 查看容器内网络
docker exec ragflow-server netstat -tlnp
```

### 健康检查
```bash
# 检查容器健康状态
docker inspect ragflow-server | grep -A 10 "Health"

# 测试Web服务
curl -I http://110.40.44.80:3010

# 测试API服务
curl -I http://110.40.44.80:5679

# 检查依赖服务连通性
curl -I http://110.40.44.80:1200  # Elasticsearch
curl -I http://110.40.44.80:9000  # MinIO
```

---

## 📊 监控指标

### 资源使用
```bash
# 查看容器资源使用
docker stats ragflow-server

# 查看容器磁盘使用
docker exec ragflow-server df -h

# 查看容器内存使用
docker exec ragflow-server free -h
```

### 性能监控
- **CPU使用率**: 通常在5-20%
- **内存使用**: 建议至少4GB可用内存
- **磁盘I/O**: 主要来自文档处理和索引
- **网络流量**: 取决于用户访问量

---

## 🔧 故障排查

### 常见问题

#### 1. 容器启动失败
```bash
# 检查容器日志
docker logs ragflow-server

# 检查依赖服务状态
docker ps | grep -E "(mysql|elasticsearch|minio|redis)"

# 检查端口占用
netstat -tlnp | grep -E "(3010|5679|8443|9380|9382)"
```

#### 2. 访问超时或无响应
```bash
# 检查容器是否运行
docker ps | grep ragflow-server

# 检查容器健康状态
docker inspect ragflow-server | grep Health

# 重启容器
docker restart ragflow-server
```

#### 3. 数据库连接问题
```bash
# 测试MySQL连接
docker exec ragflow-server mysql -h ragflow-mysql -u root -p

# 检查MySQL容器状态
docker ps | grep ragflow-mysql
```

#### 4. 搜索功能异常
```bash
# 检查Elasticsearch状态
curl http://110.40.44.80:1200/_cluster/health

# 重启Elasticsearch
docker restart ragflow-es-01
```

### 日志分析
```bash
# 查找错误日志
docker logs ragflow-server 2>&1 | grep -i error

# 查找警告信息
docker logs ragflow-server 2>&1 | grep -i warning

# 查找特定时间段日志
docker logs ragflow-server --since="2025-08-01T09:00:00" --until="2025-08-01T10:00:00"
```

---

## 💾 备份与恢复

### 数据备份
```bash
# 备份MySQL数据
docker exec ragflow-mysql mysqldump -u root -p --all-databases > ragflow_backup_$(date +%Y%m%d).sql

# 备份MinIO数据 (需要mc客户端)
# mc mirror ragflow-minio/bucket ./backup/minio/

# 备份配置文件
docker cp ragflow-server:/app/config ./backup/config/
```

### 配置备份
```bash
# 导出容器配置
docker inspect ragflow-server > ragflow-server-config.json

# 备份docker-compose配置 (如果使用)
cp docker-compose.yml backup/docker-compose-$(date +%Y%m%d).yml
```

---

## 🔄 更新维护

### 版本更新
```bash
# 拉取最新镜像
docker pull infiniflow/ragflow:latest

# 停止当前容器
docker stop ragflow-server

# 备份当前容器
docker commit ragflow-server ragflow-backup-$(date +%Y%m%d)

# 使用新镜像启动容器
docker run -d --name ragflow-server-new [相同参数] infiniflow/ragflow:latest

# 验证新版本正常后删除旧容器
docker rm ragflow-server
docker rename ragflow-server-new ragflow-server
```

### 定期维护
- **每日**: 检查容器状态和日志
- **每周**: 清理无用的日志文件
- **每月**: 备份重要数据
- **季度**: 检查版本更新

---

## 📞 应急联系

### 重启顺序 (如需完全重启)
1. 停止RAG Flow主服务
2. 检查依赖服务状态
3. 按需重启依赖服务
4. 启动RAG Flow主服务
5. 验证所有功能正常

### 快速恢复命令
```bash
# 一键重启所有RAG Flow相关服务
docker restart ragflow-redis ragflow-mysql ragflow-es-01 ragflow-minio ragflow-server
```

---

## 更新日志
- 2025-08-01: 创建RAG Flow维护文档
- 2025-08-01: 记录当前运行状态和配置信息
