# Docker应用维护文档

**服务器**: 110.40.44.80  
**创建时间**: 2025年8月1日  
**最后更新**: 2025年8月1日

---

## 📋 应用概览

| 应用名称 | 容器名称 | 镜像 | 状态 | 主要端口 | 用途 |
|---------|---------|------|------|---------|------|
| RAG Flow | ragflow-server | infiniflow/ragflow:v0.19.1-slim | 运行中 | 3010:80 | AI文档问答系统 |
| Open WebUI | openwebui | ghcr.io/open-webui/open-webui:main | 运行中 | 3000:8080 | AI聊天界面 |
| n8n | n8n | n8nio/n8n:latest | 运行中 | 5678:5678 | 工作流自动化 |
| PlayEdu | playedu-playedu-1 | playedu/light:2.0 | 运行中 | 9700:9898 | 在线教育平台 |
| Elasticsearch | ragflow-es-01 | elasticsearch:8.11.3 | 运行中 | 1200:9200 | 搜索引擎 |
| MinIO | ragflow-minio | minio/minio | 运行中 | 9000-9001 | 对象存储 |
| Redis | ragflow-redis | valkey/valkey:8 | 运行中 | 6380:6379 | 缓存数据库 |
| MySQL(RAG) | ragflow-mysql | mysql:8.0.39 | 运行中 | 5455:3306 | RAG Flow数据库 |
| MySQL(Edu) | playedu-mysql-1 | playedu-mysql | 运行中 | 23307:3306 | 教育平台数据库 |

---

## 1. RAG Flow 系统

### 1.1 基本信息
- **容器名**: ragflow-server
- **镜像**: infiniflow/ragflow:v0.19.1-slim
- **访问地址**: http://110.40.44.80:3010
- **宝塔代理**: ragflow:23cc.cn
- **功能**: AI文档问答和知识库管理

### 1.2 端口映射
- 3010:80 (Web界面)
- 5679:5679 (API服务)
- 5680:5678 (内部通信)
- 8443:443 (HTTPS)
- 9380:9380 (管理接口)
- 9382:9382 (MCP服务)

### 1.3 依赖服务
- **数据库**: ragflow-mysql (端口5455)
- **搜索引擎**: ragflow-es-01 (端口1200)
- **对象存储**: ragflow-minio (端口9000-9001)
- **缓存**: ragflow-redis (端口6380)

### 1.4 维护命令
```bash
# 查看日志
docker logs ragflow-server

# 重启服务
docker restart ragflow-server

# 进入容器
docker exec -it ragflow-server bash

# 检查健康状态
docker inspect ragflow-server | grep Health
```

### 1.5 常见问题
- **启动慢**: 依赖服务较多，需等待所有依赖服务启动
- **内存占用**: 建议至少4GB内存
- **数据备份**: 重要数据存储在MySQL和MinIO中

---

## 2. Open WebUI

### 2.1 基本信息
- **容器名**: openwebui
- **镜像**: ghcr.io/open-webui/open-webui:main
- **访问地址**: http://110.40.44.80:3000
- **宝塔代理**: openwebui:23cc.cn
- **功能**: AI聊天界面，支持多种AI模型

### 2.2 端口映射
- 3000:8080 (Web界面)

### 2.3 维护命令
```bash
# 查看日志
docker logs openwebui

# 重启服务 (注意：可能产生僵尸进程)
docker restart openwebui

# 进入容器
docker exec -it openwebui bash

# 检查健康状态
docker ps | grep openwebui
```

### 2.4 已知问题
- **僵尸进程**: 容器内curl/jq命令可能产生僵尸进程，需定期重启
- **最后重启**: 2025-08-01 09:12 (解决僵尸进程问题)

---

## 3. n8n 工作流自动化

### 3.1 基本信息
- **容器名**: n8n
- **镜像**: n8nio/n8n:latest
- **访问地址**: http://110.40.44.80:5678
- **宝塔代理**: n8n:23cc.cn
- **功能**: 工作流自动化和API集成

### 3.2 端口映射
- 5678:5678 (Web界面和API)

### 3.3 维护命令
```bash
# 查看日志
docker logs n8n

# 重启服务
docker restart n8n

# 进入容器
docker exec -it n8n sh

# 备份工作流
docker exec n8n n8n export:workflow --all --output=/tmp/workflows.json
```

---

## 4. PlayEdu 教育平台

### 4.1 基本信息
- **容器名**: playedu-playedu-1
- **镜像**: registry.cn-hangzhou.aliyuncs.com/playedu/light:2.0
- **访问地址**: http://110.40.44.80:9700
- **宝塔代理**: edu:23cc.cn
- **功能**: 在线教育和课程管理

### 4.2 端口映射
- 9700:9898 (主要Web界面)
- 9800-9801:9800-9801 (附加服务)
- 9900:9900 (管理接口)

### 4.3 依赖服务
- **数据库**: playedu-mysql-1 (端口23307)

### 4.4 维护命令
```bash
# 查看日志
docker logs playedu-playedu-1

# 重启服务
docker restart playedu-playedu-1

# 数据库连接测试
mysql -h 110.40.44.80 -P 23307 -u root -p
```

---

## 5. 数据库服务

### 5.1 RAG Flow MySQL
- **容器名**: ragflow-mysql
- **端口**: 5455:3306
- **用途**: RAG Flow数据存储

### 5.2 PlayEdu MySQL  
- **容器名**: playedu-mysql-1
- **端口**: 23307:3306
- **用途**: 教育平台数据存储

### 5.3 Redis缓存
- **容器名**: ragflow-redis
- **镜像**: valkey/valkey:8
- **端口**: 6380:6379
- **用途**: 缓存和会话存储

---

## 6. 基础设施服务

### 6.1 Elasticsearch
- **容器名**: ragflow-es-01
- **端口**: 1200:9200
- **用途**: 全文搜索和文档索引

### 6.2 MinIO对象存储
- **容器名**: ragflow-minio
- **端口**: 9000-9001:9000-9001
- **用途**: 文件和媒体存储
- **管理界面**: http://110.40.44.80:9001

---

## 7. 维护最佳实践

### 7.1 日常检查
```bash
# 检查所有容器状态
docker ps

# 检查容器资源使用
docker stats

# 检查磁盘使用
docker system df
```

### 7.2 备份策略
- **数据库**: 定期导出MySQL数据
- **配置文件**: 备份docker-compose.yml
- **用户数据**: 备份MinIO存储的文件

### 7.3 更新策略
- **镜像更新**: 定期检查新版本
- **安全补丁**: 及时应用安全更新
- **测试环境**: 先在测试环境验证

### 7.4 故障排查
1. 检查容器日志
2. 验证端口连通性
3. 检查依赖服务状态
4. 查看系统资源使用情况

---

## 更新日志
- 2025-08-01: 创建Docker应用维护文档
- 2025-08-01: 记录Open WebUI僵尸进程问题及解决方案
