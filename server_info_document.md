# 服务器系统信息收集文档

**服务器标识**: root@20240602  
**文档创建时间**: 2025年8月1日  
**最后更新时间**: 2025年8月1日 08:55:00 CST

---

## 1. 服务器基本信息

### 1.1 CPU信息
- **处理器型号**: Intel(R) Xeon(R) CPU E5-2660 v2 @ 2.20GHz
- **架构**: x86_64
- **CPU核心数**: 40核心（2个物理CPU，每个10核心，支持超线程）
- **Socket数量**: 2
- **每个Socket核心数**: 10
- **每个核心线程数**: 2
- **最大频率**: 3000.0000 MHz
- **最小频率**: 1200.0000 MHz
- **当前频率**: 43%
- **虚拟化支持**: VT-x
- **NUMA节点**: 2个
  - NUMA node0 CPU(s): 0-9,20-29
  - NUMA node1 CPU(s): 10-19,30-39

### 1.2 缓存信息
- **L1d缓存**: 640 KiB (20个实例)
- **L1i缓存**: 640 KiB (20个实例)
- **L2缓存**: 5 MiB (20个实例)
- **L3缓存**: 50 MiB (2个实例)

### 1.3 内存信息
- **总内存**: 64GB (65,801,900 kB)
- **已使用内存**: 13GB
- **可用内存**: 49GB (51,861,812 kB)
- **空闲内存**: 22GB (23,900,532 kB)
- **缓冲区**: 1.5GB (1,569,260 kB)
- **缓存**: 24GB (25,226,304 kB)
- **交换分区**: 0B (未配置)

### 1.4 安全漏洞缓解措施
- **Meltdown**: 已缓解 (PTI)
- **Spectre v1**: 已缓解 (usercopy/swapgs barriers)
- **Spectre v2**: 已缓解 (Retpolines, IBPB, IBRS_FW, STIBP)
- **L1tf**: 已缓解 (PTE Inversion, VMX条件缓存刷新)
- **MDS**: 已缓解 (Clear CPU buffers)

---

## 2. 磁盘信息

### 2.1 磁盘硬件信息
- **主磁盘**: /dev/sda (893.14 GiB / 958,999,298,048 bytes)
- **总扇区数**: 1,873,045,504 sectors

### 2.2 分区结构
- **sda1**: 1M (引导分区)
- **sda2**: 1G (boot分区，已使用68M，8%使用率)
- **sda3**: 1000M (EFI分区，几乎未使用，1%使用率)
- **sda4**: 891.2G (根分区，已使用30G，4%使用率)

### 2.3 文件系统使用情况
- **根分区 (/)**: 877G总容量，已使用30G，可用803G (4%使用率)
- **Boot分区 (/boot)**: 974M总容量，已使用68M，可用840M (8%使用率)
- **EFI分区 (/boot/efi)**: 998M总容量，几乎未使用 (1%使用率)
- **内存文件系统**:
  - tmpfs (/run): 6.3G，使用2.1M
  - tmpfs (/dev/shm): 32G，使用3.5M
  - udev (/dev): 32G，未使用

### 2.4 Docker存储
- 检测到多个Docker overlay2文件系统挂载点，表明有多个容器在运行

---

## 3. 网络配置

### 3.1 主网络接口
- **eth0** (主网卡):
  - IP地址: ************/24
  - 广播地址: *************
  - MAC地址: 00:25:90:a6:29:cc
  - 状态: UP (活跃)
  - 别名: eno1, enp2s0f0

- **eth1** (备用网卡):
  - MAC地址: 00:25:90:a6:29:cd
  - 状态: DOWN (未启用)
  - 别名: eno2, enp2s0f1

### 3.2 Docker网络接口
- **docker0**: **********/16 (默认Docker桥接网络)
- **br-02f51d0f1131**: **********/16 (自定义Docker网络)
- **br-82edd9734e5b**: **********/16 (自定义Docker网络)
- **br-26c29d2c4702**: **********/16 (自定义Docker网络，当前DOWN状态)

### 3.3 虚拟网络接口
- 检测到多个veth接口，表明有多个Docker容器在运行并连接到不同的网络

### 3.4 路由配置
- **默认网关**: *********** (通过eth0)
- **本地网络**: ***********/24
- **Docker网络路由**:
  - **********/16 → docker0
  - **********/16 → br-82edd9734e5b
  - **********/16 → br-26c29d2c4702
  - **********/16 → br-02f51d0f1131

### 3.5 DNS配置
- **主DNS**: ********* (阿里云DNS)
- **备用DNS**: 66.66.66.66

---

## 4. 操作系统信息

### 4.1 系统版本
- **操作系统**: Debian GNU/Linux 12 (bookworm)
- **版本代号**: bookworm
- **版本ID**: 12
- **内核版本**: Linux 6.1.0-25-amd64 #1 SMP PREEMPT_DYNAMIC Debian 6.1.106-3
- **架构**: x86_64 GNU/Linux
- **主机名**: 20240602

### 4.2 系统运行状态
- **系统运行时间**: 3天17小时15分钟
- **当前负载**: 0.05, 0.09, 0.09 (1分钟, 5分钟, 15分钟平均负载)
- **运行进程数**: 640个进程
- **当前用户数**: 1个用户在线

### 4.3 核心系统服务状态
- **systemd-journald**: 运行中 (日志服务)
- **systemd-udevd**: 运行中 (设备管理)
- **systemd-logind**: 运行中 (用户登录管理)
- **dbus**: 运行中 (系统消息总线)
- **rsyslog**: 运行中 (系统日志)
- **cron**: 运行中 (定时任务)
- **ssh**: 运行中 (SSH服务)
- **docker**: 运行中 (Docker容器引擎)
- **containerd**: 运行中 (容器运行时)

---

## 5. 宝塔面板配置

### 5.1 安装状态
- **安装状态**: ✅ 已安装
- **版本**: 11.0.0
- **Python版本**: 支持当前系统Python版本
- **面板端口**: 34268

### 5.2 服务状态
- **服务状态**: ✅ 已启动并设置开机自启
- **服务名称**: bt.service
- **启动方式**: LSB服务 (通过/etc/init.d/bt管理)
- **最后操作**: 2025-08-01 手动启动服务

### 5.3 访问信息
- **面板地址**: https://************:34268/e187fd2e
- **用户名**: tg8xkx5c
- **密码**: ******** (已隐藏，通过bt default命令查看)
- **状态**: ✅ 正常运行，可以访问
- **注意**: 初始密码仅在首次登录前有效，后续可通过 `bt 5` 命令修改

### 5.4 宝塔面板网站管理配置
- **管理的网站数量**: 7个
- **Docker集成**: ✅ 已集成Docker管理功能
- **反向代理配置**:
  1. **ragflow-mcp:23cc.cn** - MCP服务代理
  2. **edu-n:23cc.cn** - 教育平台服务
  3. **edu:23cc.cn** - 教育平台PC端
  4. **edu-admin:23cc.cn** - 教育平台管理后台
  5. **ragflow:23cc.cn** - RAG Flow服务代理
  6. **openwebui:23cc.cn** - Open WebUI代理
  7. **n8n:23cc.cn** - n8n工作流代理

### 5.5 安全配置
- **WAF防护**: ✅ 已启用
- **SSL证书**: 各网站可独立配置
- **访问控制**: 支持IP白名单和访问限制

---

## 6. Docker环境信息

### 6.1 Docker版本信息
- **Docker版本**: 26.1.4 (build 5650f9b)
- **Docker Compose版本**: v2.34.0
- **存储驱动**: overlay2
- **日志驱动**: json-file
- **Cgroup驱动**: systemd

### 6.2 容器统计
- **总容器数**: 9个
- **运行中**: 9个
- **暂停**: 0个
- **停止**: 0个
- **镜像总数**: 11个

### 6.3 运行中的容器详情
1. **playedu-playedu-1** (PlayEdu教育平台)
   - 镜像: registry.cn-hangzhou.aliyuncs.com/playedu/light:2.0
   - 端口: 9700:9898, 9800-9801:9800-9801, 9900:9900
   - 运行时间: 34小时

2. **playedu-mysql-1** (PlayEdu数据库)
   - 镜像: playedu-mysql
   - 端口: 23307:3306
   - 运行时间: 34小时

3. **openwebui** (Open WebUI)
   - 镜像: ghcr.io/open-webui/open-webui:main
   - 端口: 3000:8080
   - 状态: 健康运行 (2天)

4. **n8n** (工作流自动化)
   - 镜像: n8nio/n8n:latest
   - 端口: 5678:5678
   - 运行时间: 2天

5. **ragflow-server** (RAG Flow服务)
   - 镜像: infiniflow/ragflow:v0.19.1-slim
   - 端口: 3010:80, 5679:5679, 5680:5678, 8443:443, 9380:9380, 9382:9382
   - 运行时间: 2天

6. **ragflow-mysql** (RAG Flow数据库)
   - 镜像: mysql:8.0.39
   - 端口: 5455:3306
   - 状态: 健康运行 (2天)

7. **ragflow-es-01** (Elasticsearch)
   - 镜像: elasticsearch:8.11.3
   - 端口: 1200:9200
   - 状态: 健康运行 (2天)

8. **ragflow-minio** (MinIO对象存储)
   - 镜像: quay.io/minio/minio:RELEASE.2025-06-13T11-33-47Z
   - 端口: 9000-9001:9000-9001
   - 状态: 健康运行 (2天)

9. **ragflow-redis** (Redis缓存)
   - 镜像: valkey/valkey:8
   - 端口: 6380:6379
   - 状态: 健康运行 (2天)

---

## 7. Go语言网站部署情况

### 7.1 Go环境状态
- **Go安装状态**: ❌ 未安装或不在PATH中
- **Go相关进程**: 未发现
- **Go项目文件**: 未在常见目录中发现.go或go.mod文件

### 7.2 可能的Go网站端口
- **端口3000**: 被Docker容器占用 (openwebui)
- **其他常见Go端口**: 未发现独立的Go网站服务

### 7.3 部署建议
- 如需部署Go网站，需要先安装Go环境
- 建议使用Docker容器化部署Go应用
- 可考虑使用端口8080、8000、4000、5000等未占用端口

---

## 8. 网络安全配置

### 8.1 防火墙状态
- **UFW防火墙**: 已安装但未启用 (inactive/dead)
- **iptables**: 有规则配置，主要用于Docker网络管理
- **默认策略**: INPUT和FORWARD链默认DROP，相对安全

### 8.2 开放端口列表
**系统服务端口:**
- **22**: SSH服务 (sshd)
- **25**: 邮件服务 (exim4) - 仅本地监听
- **80**: HTTP (nginx)
- **443**: HTTPS (nginx)
- **888**: 宝塔面板备用端口 (nginx)
- **34268**: 宝塔面板主端口 (BT-Panel)

**Docker容器端口:**
- **1200**: Elasticsearch (ragflow-es-01)
- **3000**: Open WebUI
- **5455**: RAG Flow MySQL
- **5678**: n8n工作流
- **5679, 5680**: RAG Flow服务
- **6380**: Redis (ragflow-redis)
- **8443**: RAG Flow HTTPS
- **9000-9001**: MinIO对象存储
- **9380, 9382**: RAG Flow附加服务
- **9700, 9800-9801, 9900**: PlayEdu教育平台
- **23307**: PlayEdu MySQL

### 8.3 安全建议
- ⚠️ 建议启用UFW防火墙并配置适当规则
- ⚠️ 考虑更改SSH默认端口22
- ⚠️ 宝塔面板端口34268建议限制访问IP
- ✅ 邮件服务仅本地监听，安全性较好

---

## 9. 系统资源使用情况

### 9.1 CPU使用情况
- **当前负载**: 0.06, 0.09, 0.09 (1分钟, 5分钟, 15分钟)
- **CPU使用率**: 用户态2.0%, 系统态1.0%, 空闲97.0%
- **负载状态**: 轻负载，系统运行良好

### 9.2 内存使用情况
- **总内存**: 64,259.7 MB (约64GB)
- **已使用**: 13,453.8 MB (约13GB, 21%)
- **空闲内存**: 23,501.6 MB (约23GB, 37%)
- **缓冲/缓存**: 28,047.8 MB (约28GB, 44%)
- **可用内存**: 50,805.8 MB (约51GB, 79%)
- **交换分区**: 0 MB (未配置)

### 9.3 进程状态
- **总进程数**: ~293个
- **运行中**: 0个
- **睡眠**: 293个
- **停止**: 0个
- **僵尸进程**: ✅ 已完全清理 (2025-08-01 09:12通过重启openwebui容器解决)

### 9.4 资源消耗最高进程
- **Java进程** (PID: 1071609): 使用4.6GB内存，CPU使用率5.9%
- **系统整体状态**: 资源充足，运行稳定

---

## 10. 已安装软件包列表

### 10.1 开发工具
- **Git**: 2.39.5-0+deb12u2 (版本控制)
- **Python3**: 3.11.2 (编程语言)
- **curl**: 7.88.1-10+deb12u12 (数据传输工具)
- **wget**: 1.21.3-1+deb12u1 (文件下载工具)

### 10.2 Web服务
- **Nginx**: 已安装并运行 (Web服务器)
- **Docker**: 26.1.4 (容器化平台)
- **Docker Compose**: v2.34.0 (容器编排)

### 10.3 数据库
- **MySQL**: 通过Docker容器运行多个实例
- **Redis**: 通过Docker容器运行 (Valkey 8)
- **Elasticsearch**: 8.11.3 (搜索引擎)

### 10.4 缺失的常用工具
- **Node.js**: 未安装
- **npm**: 未安装
- **Go**: 未安装
- **Java**: 通过Docker容器运行

---

## 11. 服务访问地址汇总

### 11.1 管理面板
- **宝塔面板**: https://************:34268/e187fd2e (用户名: tg8xkx5c)
- **MinIO控制台**: http://************:9001

### 11.2 应用服务 (通过宝塔反向代理配置)
- **Open WebUI**: http://************:3000
  - 反向代理: /www/dk_project/wwwroot/openwebui:23cc.cn
  - 容器: openwebui_45rq-openwebui
  - 状态: 正常运行

- **n8n工作流**: http://************:5678
  - 反向代理: /www/dk_project/wwwroot/n8n:23cc.cn
  - 容器: wm4-n8n (XM4-1版)
  - 状态: 正常运行

- **RAG Flow**: http://************:3010
  - 反向代理: /www/dk_project/wwwroot/ragflow:23cc.cn
  - 容器: ragflow-server的80端口代理
  - 状态: 正常运行

- **PlayEdu教育平台**: http://************:9700
  - 反向代理: /www/dk_project/wwwroot/edu:23cc.cn
  - 容器: PC端访问
  - 状态: 正常运行

- **PlayEdu管理后台**:
  - 反向代理: /www/dk_project/wwwroot/edu-admin:23cc.cn
  - 容器: 管理后台
  - 状态: 正常运行

- **Elasticsearch**: http://************:1200
  - 反向代理: /www/dk_project/wwwroot/ragflow-mcp:23cc.cn
  - 容器: MCP服务
  - 状态: 正常运行

### 11.3 数据库连接
- **RAG Flow MySQL**: ************:5455
- **PlayEdu MySQL**: ************:23307
- **Redis**: ************:6380

### 11.4 宝塔面板网站配置
- **网站总数**: 7个
- **反向代理配置**: 全部通过/www/dk_project/wwwroot/目录管理
- **SSL状态**: 各网站SSL证书状态可在面板中查看
- **访问协议**: 支持HTTP和HTTPS
- **WAF防护**: 已启用Web应用防火墙

---

## 更新日志
- 2025-08-01 08:55: 创建文档，添加CPU和内存基本信息
- 2025-08-01 08:56: 添加磁盘和网络配置信息
- 2025-08-01 08:57: 添加操作系统、宝塔面板和Docker环境信息
- 2025-08-01 08:58: 完成网络安全配置、系统资源使用情况和软件包信息收集
- 2025-08-01 09:00: 启动宝塔面板服务并设置开机自启
- 2025-08-01 09:03: 确认宝塔面板正常运行，获取登录信息
- 2025-08-01 09:12: 成功清理42个僵尸进程（通过重启openwebui容器解决）
- 2025-08-01 09:15: 添加宝塔面板网站管理和反向代理配置信息
